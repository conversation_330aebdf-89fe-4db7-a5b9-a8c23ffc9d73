from app.views.ViewsBase import *
from django.utils.encoding import escape_uri_path
import os
import time
import subprocess
from app.models import *


def download(request):
    params = parse_get_params(request)
    filename = params.get("filename")
    try:
        if filename:
            if filename.endswith(".mp4") \
                    or filename.endswith(".jpg") \
                    or filename.endswith(".pt") \
                    or filename.endswith(".xml") \
                    or filename.endswith(".bin") \
                    or filename.endswith(".csv") \
                    or filename.endswith(".zip") \
                    or filename.endswith(".xcsettings") \
                    or filename.endswith(".xclogs"):


                    if filename.endswith(".pt") :

                        tradecode=filename.replace("/train/weights/last.pt","").replace("train/","")

                        train = TaskTrain.objects.filter(code=tradecode)
                        if len(train) > 0:
                            train = train[0]
                        else:
                            raise Exception("该训练任务不存在！")

                        # yolo export model=yolov8n.pt format=openvino
                        yolo8_install_dir = getattr(g_config, train.algorithm_code)["install_dir"]
                        # yolo8_venv = getattr(g_config, train.algorithm_code)["venv"]
                        # yolo8_venv = "F:\\xclabel\\xclabel-main\\venv\\Scripts\\activate.bat"
                        # yolo8_name = getattr(g_config, train.algorithm_code)["name"]
                       # yolo8_model = os.path.join(yolo8_install_dir, getattr(g_config, train.algorithm_code)["model"])
                        # E:\project\bxc\gitee\xclabel\static\storage\train\train20250415154157\train
                        # 'E:\\project\\bxc\\gitee\\xclabel\\static\\storage/train/train20250415154157\\datasets\\data.yaml'
                        modelpath=train.train_datasets.replace("\\datasets\\data.yaml","\\train\\weights")

                        osSystem = OSSystem()
                        if osSystem.getSystemName() == "Windows":
                            # Windows系统，需要执行下切换盘符的步骤
                            dirve, tail = os.path.splitdrive(yolo8_install_dir)
                            cd_dirve = "%s &&" % dirve
                        else:
                            cd_dirve = ""
                        __command_run = "yolo export model=last.pt format=openvino"
                        __predict_command = "{cd_dirve} cd {yolo8_install_dir}  && {command_run}".format(
                            cd_dirve=cd_dirve,
                            yolo8_install_dir=modelpath,
                            command_run=__command_run
                        )
                        g_logger.info("测试模型命令行：%s" % __predict_command)

                        # proc = subprocess.Popen(__predict_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                        #                         text=True, encoding='utf-8')
                        proc = subprocess.Popen(__predict_command, shell=True, stdout=subprocess.PIPE,
                                                stderr=subprocess.PIPE,
                                                text=True, encoding='gbk')

                        stdout, stderr = proc.communicate()

                        # # winget install GnuWin32.zip
                        # file_path = os.path.join(modelpath, "last_openvino_model")
                        # zip_path = os.path.join(modelpath, "last_openvino_model.zip")
                        # # compress_to_zip(file_path, zip_path)
                        # os.system("zip -r {0} {1}".format(file_path,zip_path))
                        #
                        #
                        #
                        # if os.path.exists(zip_path):
                        #     f = open(zip_path, mode="rb")
                        #     data = f.read()
                        #     f.close()
                        #     response = HttpResponse(data, content_type="application/octet-stream")
                        #     response['Access-Control-Allow-Origin'] = "*"
                        #     response['Access-Control-Allow-Headers'] = "*"
                        #     response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        #     response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))
                        #     try:
                        #         os.remove(zip_path)
                        #     except Exception as e:
                        #         g_logger.error("storage/download completed, but delete error,filepath=%s : %s" % (zip_path, str(e)))
                        #     return response

                        file_path = os.path.join(modelpath, "last_openvino_model","last.bin")
                        f = open(file_path, mode="rb")
                        data = f.read()
                        f.close()
                        response = HttpResponse(data, content_type="application/octet-stream")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Disposition'] = "attachment;filename={};".format(
                            escape_uri_path("last.bin"))
                        try:
                            os.remove(file_path)
                        except Exception as e:
                            g_logger.error("storage/download completed, but delete error,filepath=%s : %s" % (
                            file_path, str(e)))
                        return response


                    else  :
                        filepath = os.path.join(g_config.storageDir, filename)

                        if os.path.exists(filepath):
                            f = open(filepath, mode="rb")
                            data = f.read()
                            f.close()
                            response = HttpResponse(data, content_type="application/octet-stream")
                            response['Access-Control-Allow-Origin'] = "*"
                            response['Access-Control-Allow-Headers'] = "*"
                            response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                            response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))
                            try:
                                os.remove(filepath)
                            except Exception as e:
                                g_logger.error("storage/download completed, but delete error,filepath=%s : %s" % (filepath, str(e)))
                            return response
                        else:
                            raise Exception("storage/download filepath not found")
            else:

                raise Exception("storage/download unsupported filename format")
        else:
            raise Exception("storage/download filename not found")

    except Exception as e:
        return HttpResponseJson({"msg": str(e)})

def access(request):
    params = parse_get_params(request)
    filename = params.get("filename")
    try:
        if filename:
            if (filename.endswith(".avi") or filename.endswith(".flv") or filename.endswith(".mp4")
                    or filename.endswith(".jpg") or filename.endswith(".png")  or filename.endswith(".jpeg")
                    or filename.endswith(".pt") or filename.endswith(".bin") or filename.endswith(".xml")):
                filepath = os.path.join(g_config.storageDir, filename)
                if os.path.exists(filepath):
                    f = open(filepath, mode="rb")
                    data = f.read()
                    f.close()

                    if filename.endswith(".mp4"):
                        fs = os.path.getsize(filepath)
                        response = HttpResponse(data, content_type="video/mp4")
                        response['Accept-Ranges'] = "bytes"
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Length'] = fs
                        # response['Content-Range'] = "bytes 32768-188720863/188720864"
                        # response['Etag'] = "66dc3116-b3fa6e0"
                        # response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))
                    elif filename.endswith(".jpg") or filename.endswith(".png") or filename.endswith(".jpeg"):
                        response = HttpResponse(data, content_type="image/jpeg")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                    elif filename.endswith(".pt") or filename.endswith(".bin") or filename.endswith(".xml"):
                        # 对于模型文件，只返回文件存在的确认，不返回文件内容
                        response = HttpResponseJson({"msg": "file exists", "exists": True})
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        return response
                    else:
                        response = HttpResponse(data, content_type="application/octet-stream")
                        response['Access-Control-Allow-Origin'] = "*"
                        response['Access-Control-Allow-Headers'] = "*"
                        response['Access-Control-Allow-Methods'] = "POST, GET, OPTIONS, DELETE"
                        response['Content-Disposition'] = "attachment;filename={};".format(escape_uri_path(filename))

                    return response
                else:
                    raise Exception("storage/folder filepath not found")
            else:
                raise Exception("storage/folder unsupported filename format")
        else:
            raise Exception("storage/folder filename not exist")

    except Exception as e:
        return HttpResponseJson({"msg": str(e)})