import os
import json
import subprocess
import tempfile
import shutil
from PIL import Image

from app.views.ViewsBase import g_logger
from app.models import TaskSample
from datetime import datetime


class YoloPredict:
    """YOLO模型预测工具类"""

    # 类别名称映射，根据模型名称或任务名称自动选择合适的类别名称
    CLASS_NAME_MAPPINGS = {
        "t68zj_fok": {0: "left", 1: "right"},  # 从模型中提取的真实类别名称
        "aec_pack": {0: "Normal", 1: "Defect"},
        "hz": {0: "Good", 1: "Bad"},
        "defect": {0: "Pass", 1: "Fail"},
        # 可以根据需要添加更多映射
    }

    @staticmethod
    def extract_class_names_from_model(model_path):
        """
        从YOLO模型文件中动态提取类别名称

        Args:
            model_path: 模型文件路径

        Returns:
            dict: 类别ID到名称的映射，如果提取失败返回None
        """
        try:
            import subprocess
            import tempfile

            # 创建临时Python脚本来加载模型
            script_content = f'''
import sys
try:
    from ultralytics import YOLO
    model = YOLO("{model_path}")
    if hasattr(model, 'names'):
        names = model.names
        print("MODEL_NAMES:", names)
    elif hasattr(model.model, 'names'):
        names = model.model.names
        print("MODEL_NAMES:", names)
    else:
        print("MODEL_NAMES: NOT_FOUND")
except Exception as e:
    print("ERROR:", str(e))
'''

            # 写入临时文件并执行
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(script_content)
                temp_script = f.name

            try:
                # 在cv1环境中执行脚本
                cmd = f"python {temp_script}"
                result = subprocess.run(
                    cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    timeout=60
                )

                if result.returncode == 0:
                    output = result.stdout
                    for line in output.split('\n'):
                        if line.startswith('MODEL_NAMES:'):
                            names_str = line.replace('MODEL_NAMES:', '').strip()
                            if names_str != 'NOT_FOUND':
                                try:
                                    names = eval(names_str)
                                    if isinstance(names, dict):
                                        g_logger.info(f"从模型中提取到类别名称: {names}")
                                        return names
                                except:
                                    pass

            finally:
                # 清理临时文件
                try:
                    os.unlink(temp_script)
                except:
                    pass

        except Exception as e:
            g_logger.warning(f"从模型中提取类别名称失败: {str(e)}")

        return None

    @staticmethod
    def get_class_names_for_model(model_path, task_name=""):
        """
        根据模型路径或任务名称获取合适的类别名称映射

        Args:
            model_path: 模型文件路径
            task_name: 任务名称

        Returns:
            dict: 类别ID到名称的映射
        """
        # 首先尝试从模型中动态提取
        dynamic_names = YoloPredict.extract_class_names_from_model(model_path)
        if dynamic_names:
            return dynamic_names

        # 如果动态提取失败，使用预定义映射
        model_name = os.path.basename(model_path).lower()
        task_name = task_name.lower()

        # 检查是否有匹配的映射
        for key, mapping in YoloPredict.CLASS_NAME_MAPPINGS.items():
            if key in model_name or key in task_name:
                return mapping

        # 默认返回通用的缺陷检测名称
        return {0: "class_0", 1: "class_1"}

    @staticmethod
    def predict_task_samples(task, model_path, storage_dir):
        """
        使用YOLO模型对任务中的所有样本进行预测，并将结果保存为标注
        
        Args:
            task: 任务对象
            model_path: YOLO模型文件路径
            storage_dir: 存储根目录
            
        Returns:
            tuple: (success, message, info)
        """
        try:
            # 验证模型文件是否存在
            if not os.path.exists(model_path):
                return False, f"模型文件不存在: {model_path}", {}
            
            # 验证模型文件格式
            if not model_path.endswith('.pt'):
                return False, "只支持 .pt 格式的YOLO模型文件", {}
            
            # 获取任务的所有样本
            samples = TaskSample.objects.filter(task_code=task.code)
            if not samples:
                return False, "任务中没有样本数据", {}
            
            # 创建临时目录用于预测
            temp_dir = tempfile.mkdtemp(prefix="yolo_predict_")
            images_dir = os.path.join(temp_dir, "images")
            os.makedirs(images_dir, exist_ok=True)
            
            try:
                # 复制图片到临时目录
                sample_paths = {}  # 存储样本代码和图片路径的映射
                for sample in samples:
                    src_image_path = os.path.join(storage_dir, "task", task.code, "sample", sample.new_filename)
                    if os.path.exists(src_image_path):
                        dst_image_path = os.path.join(images_dir, sample.new_filename)
                        shutil.copy(src_image_path, dst_image_path)
                        sample_paths[sample.new_filename] = sample.code
                
                if not sample_paths:
                    return False, "没有找到有效的图片文件", {}
                
                # 执行YOLO预测
                predict_dir = os.path.join(temp_dir, "predict")
                success, msg = YoloPredict._run_yolo_predict(model_path, images_dir, predict_dir)
                if not success:
                    return False, msg, {}
                
                # 获取类别名称映射
                class_names = YoloPredict.get_class_names_for_model(model_path, task.name)

                # 先更新任务标签，确保有正确的颜色映射
                YoloPredict._update_task_labels(task, set(), class_names)

                # 解析预测结果并保存为标注
                success, msg, info = YoloPredict._parse_and_save_predictions(
                    task, predict_dir, sample_paths, storage_dir, class_names
                )
                
                return success, msg, info
                
            finally:
                # 清理临时目录
                try:
                    shutil.rmtree(temp_dir)
                except:
                    pass
                    
        except Exception as e:
            g_logger.error(f"YOLO预测失败: {str(e)}")
            return False, f"预测过程中发生错误: {str(e)}", {}
    
    @staticmethod
    def _run_yolo_predict(model_path, source_dir, output_dir):
        """
        运行YOLO预测命令
        
        Args:
            model_path: 模型文件路径
            source_dir: 输入图片目录
            output_dir: 输出目录
            
        Returns:
            tuple: (success, message)
        """
        try:
            # 构建在conda环境中执行YOLO预测的命令
            yolo_command = f"yolo detect predict model={model_path} source={source_dir} project={output_dir} save_txt=True save_conf=True"

            # 在cv1环境中执行命令
            full_command = f"{yolo_command}"

            g_logger.info(f"执行YOLO预测命令: {full_command}")

            # 执行命令
            result = subprocess.run(
                full_command,
                shell=True,  # 使用shell执行conda命令
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            if result.returncode == 0:
                g_logger.info("YOLO预测执行成功")
                return True, "预测成功"
            else:
                error_msg = result.stderr or result.stdout or "未知错误"
                g_logger.error(f"YOLO预测失败: {error_msg}")
                return False, f"YOLO预测失败: {error_msg}"
                
        except subprocess.TimeoutExpired:
            return False, "预测超时，请检查模型和数据"
        except FileNotFoundError:
            error_msg = """未找到YOLO命令，请安装ultralytics包：

1. 安装ultralytics：
   pip install ultralytics

2. 验证安装：
   yolo --help

3. 确保yolo命令在系统PATH中可用"""
            g_logger.error("YOLO命令未找到，需要安装ultralytics")
            return False, error_msg
        except Exception as e:
            g_logger.error(f"执行YOLO预测时发生错误: {str(e)}")
            return False, f"执行预测时发生错误: {str(e)}"
    
    @staticmethod
    def _parse_and_save_predictions(task, predict_dir, sample_paths, storage_dir, class_names=None):
        """
        解析YOLO预测结果并保存为标注
        
        Args:
            task: 任务对象
            predict_dir: 预测结果目录
            sample_paths: 样本路径映射
            storage_dir: 存储根目录
            
        Returns:
            tuple: (success, message, info)
        """
        try:
            # 查找预测结果目录（YOLO会创建一个predict子目录）
            predict_result_dir = None
            for item in os.listdir(predict_dir):
                item_path = os.path.join(predict_dir, item)
                if os.path.isdir(item_path) and "predict" in item:
                    predict_result_dir = item_path
                    break
            
            if not predict_result_dir:
                return False, "未找到预测结果目录", {}
            
            # 查找labels目录
            labels_dir = os.path.join(predict_result_dir, "labels")
            if not os.path.exists(labels_dir):
                return False, "未找到预测标注文件", {}
            
            # 先删除所有现有标注
            TaskSample.objects.filter(task_code=task.code).update(
                annotation_state=0,
                annotation_content=None,
                annotation_time=None,
                annotation_user_id=0,  # 使用0而不是None，因为字段不允许NULL
                annotation_username=""  # 使用空字符串而不是None
            )
            
            # 获取任务现有标签
            task_labels = []
            if task.labels:
                try:
                    task_labels = json.loads(task.labels)
                except json.JSONDecodeError:
                    task_labels = []

            # 解析预测结果并收集所有使用的类别
            processed_count = 0
            annotation_count = 0
            all_class_ids = set()  # 收集所有预测中使用的类别ID

            for filename in os.listdir(labels_dir):
                if filename.endswith('.txt'):
                    # 获取对应的图片文件名
                    image_filename = filename.replace('.txt', '.jpg')
                    if image_filename not in sample_paths:
                        # 尝试其他常见格式
                        for ext in ['.png', '.jpeg', '.JPG', '.PNG', '.JPEG']:
                            test_filename = filename.replace('.txt', ext)
                            if test_filename in sample_paths:
                                image_filename = test_filename
                                break

                    if image_filename not in sample_paths:
                        continue

                    sample_code = sample_paths[image_filename]
                    label_file_path = os.path.join(labels_dir, filename)

                    # 读取标注文件并收集类别ID
                    annotations, class_ids = YoloPredict._parse_yolo_label_file(
                        label_file_path, image_filename, storage_dir, task.code, class_names, task_labels
                    )

                    # 收集所有类别ID
                    all_class_ids.update(class_ids)

                    if annotations:
                        # 保存标注到数据库
                        sample = TaskSample.objects.filter(code=sample_code).first()
                        if sample:
                            sample.annotation_content = json.dumps(annotations)
                            sample.annotation_state = 1
                            sample.annotation_time = datetime.now()
                            sample.annotation_user_id = 0  # 系统自动标注
                            sample.annotation_username = "模型预标注"
                            sample.save()
                            annotation_count += len(annotations)

                    processed_count += 1

            # 更新任务的标签列表
            YoloPredict._update_task_labels(task, all_class_ids, class_names)
            
            info = {
                "processed_count": processed_count,
                "annotation_count": annotation_count,
                "total_samples": len(sample_paths)
            }
            
            return True, f"预标注完成！处理了 {processed_count} 个样本，生成了 {annotation_count} 个标注", info
            
        except Exception as e:
            g_logger.error(f"解析预测结果失败: {str(e)}")
            return False, f"解析预测结果失败: {str(e)}", {}
    
    @staticmethod
    def _parse_yolo_label_file(label_file_path, image_filename, storage_dir, task_code, class_names=None, task_labels=None):
        """
        解析YOLO标注文件，转换为系统标注格式

        Args:
            label_file_path: 标注文件路径
            image_filename: 图片文件名
            storage_dir: 存储根目录
            task_code: 任务代码
            class_names: 类别名称映射
            task_labels: 任务标签列表

        Returns:
            tuple: (标注数据列表, 类别ID集合)
        """
        try:
            # 获取图片尺寸
            image_path = os.path.join(storage_dir, "task", task_code, "sample", image_filename)
            if not os.path.exists(image_path):
                return [], set()

            with Image.open(image_path) as img:
                image_width, image_height = img.size

            annotations = []
            class_ids = set()  # 收集所有类别ID

            with open(label_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue

                    parts = line.split()
                    if len(parts) < 5:
                        continue

                    try:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        confidence = float(parts[5]) if len(parts) > 5 else 0.5

                        # 收集类别ID
                        class_ids.add(class_id)

                        # 转换为绝对坐标
                        x_center_abs = x_center * image_width
                        y_center_abs = y_center * image_height
                        width_abs = width * image_width
                        height_abs = height * image_height

                        # 计算边界框坐标
                        x1 = x_center_abs - width_abs / 2
                        y1 = y_center_abs - height_abs / 2
                        x2 = x_center_abs + width_abs / 2
                        y2 = y_center_abs + height_abs / 2

                        # 获取真实的类别名称
                        if class_names and class_id in class_names:
                            label_name = class_names[class_id]
                        else:
                            label_name = f"class_{class_id}"

                        # 从任务标签中查找对应的颜色
                        color = "#ff0000"  # 默认红色
                        color_rgb = "255,0,0"  # 默认红色RGB

                        if task_labels:
                            # 在任务标签中查找匹配的标签
                            for label in task_labels:
                                if label.get('labelName') == label_name:
                                    color = label.get('labelColor', '#ff0000')
                                    if 'labelColorR' in label and 'labelColorG' in label and 'labelColorB' in label:
                                        color_rgb = f"{label['labelColorR']},{label['labelColorG']},{label['labelColorB']}"
                                    else:
                                        color_rgb = YoloPredict._hex_to_rgb(color)
                                    break

                        # 如果在任务标签中没找到，使用预定义颜色
                        if color == "#ff0000" and color_rgb == "255,0,0":
                            colors = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff",
                                     "#ffa500", "#800080", "#008000", "#ffc0cb"]  # 预定义颜色
                            color = colors[class_id % len(colors)]  # 循环使用颜色
                            color_rgb = YoloPredict._hex_to_rgb(color)

                        # 构建标注数据（矩形格式）
                        # 计算矩形的宽度和高度
                        width = x2 - x1
                        height = y2 - y1

                        annotation = {
                            "content": [
                                {"x": x1, "y": y1},
                                {"x": x2, "y": y1},
                                {"x": x2, "y": y2},
                                {"x": x1, "y": y2}
                            ],
                            "rectMask": {
                                "xMin": x1,
                                "yMin": y1,
                                "width": width,
                                "height": height,
                            },
                            "labels": {
                                "labelName": label_name,
                                "labelColor": color,
                                "labelColorRGB": color_rgb,
                                "visibility": True
                            },
                            "labelLocation": {
                                "x": (x1 + x2) / 2,
                                "y": (y1 + y2) / 2
                            },
                            "contentType": "rect",
                            "confidence": confidence
                        }

                        annotations.append(annotation)

                    except (ValueError, IndexError) as e:
                        g_logger.warning(f"解析标注行失败: {line}, 错误: {str(e)}")
                        continue

            return annotations, class_ids

        except Exception as e:
            g_logger.error(f"解析YOLO标注文件失败: {str(e)}")
            return [], set()

    @staticmethod
    def _hex_to_rgb(hex_color):
        """
        将十六进制颜色转换为RGB字符串格式

        Args:
            hex_color: 十六进制颜色，如 "#ff0000"

        Returns:
            str: RGB格式字符串，如 "255,0,0"
        """
        try:
            # 移除 # 符号
            hex_color = hex_color.lstrip('#')
            # 转换为RGB
            r = int(hex_color[0:2], 16)
            g = int(hex_color[2:4], 16)
            b = int(hex_color[4:6], 16)
            return f"{r},{g},{b}"
        except (ValueError, IndexError):
            # 如果转换失败，返回红色
            return "255,0,0"

    @staticmethod
    def _update_task_labels(task, class_ids, class_names=None):
        """
        更新任务的标签列表，添加预测中使用的类别

        Args:
            task: 任务对象
            class_ids: 预测中使用的类别ID集合
        """
        try:
            # 获取现有标签
            existing_labels = []
            if task.labels:
                try:
                    existing_labels = json.loads(task.labels)
                except json.JSONDecodeError:
                    g_logger.warning(f"任务 {task.code} 的标签格式无效，将重新创建")
                    existing_labels = []

            # 获取现有标签名称集合
            existing_label_names = set()
            if existing_labels:
                for label in existing_labels:
                    if isinstance(label, dict) and 'labelName' in label:
                        existing_label_names.add(label['labelName'])

            # 为新的类别ID创建标签
            colors = ["#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff",
                     "#ffa500", "#800080", "#008000", "#ffc0cb"]  # 预定义颜色

            new_labels_added = False
            for class_id in sorted(class_ids):
                # 获取真实的类别名称
                if class_names and class_id in class_names:
                    label_name = class_names[class_id]
                else:
                    label_name = f"class_{class_id}"

                # 如果标签不存在，则添加
                if label_name not in existing_label_names:
                    color = colors[class_id % len(colors)]  # 循环使用颜色
                    color_rgb = YoloPredict._hex_to_rgb(color)  # 转换为RGB格式
                    new_label = {
                        "labelName": label_name,
                        "labelColor": color,
                        "labelColorR": color_rgb.split(',')[0],
                        "labelColorG": color_rgb.split(',')[1],
                        "labelColorB": color_rgb.split(',')[2]
                    }
                    existing_labels.append(new_label)
                    existing_label_names.add(label_name)
                    new_labels_added = True
                    g_logger.info(f"为任务 {task.code} 添加新标签: {label_name} (颜色: {color})")

            # 如果添加了新标签，更新任务
            if new_labels_added:
                task.labels = json.dumps(existing_labels)
                task.save()
                g_logger.info(f"任务 {task.code} 标签列表已更新，共 {len(existing_labels)} 个标签")

        except Exception as e:
            g_logger.error(f"更新任务标签失败: {str(e)}")
