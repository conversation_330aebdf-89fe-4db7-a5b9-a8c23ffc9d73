<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <link rel="shortcut icon" href="/static/images/logo.ico?v=15">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>GGECLABEL | 标注工具</title>
    <link rel="stylesheet" href="/static/lib/label/css/label.css?v=20">
    <link rel="stylesheet" href="/static/lib/label/css/font-awesome.min.css">
    <link rel="stylesheet" href="/static/lib/label/css/switch.css">
          <link href="/static/lib/jquery/toast/1.3.2/jquery.toast.css?v=16" rel="stylesheet">
    <style>
        /* 删除图像按钮的特殊样式 */
        .deleteImageBtn span {
            background-color: #d9534f !important;
            color: white !important;
            border-color: #d43f3a !important;
        }
        .deleteImageBtn:hover span {
            background-color: #c9302c !important;
            border-color: #ac2925 !important;
        }
    </style>
</head>
<body>

<div class="LabelImage">
    <div class="toolHead">
        <div class="toolMuster">

            <div class="assistNav">
                 <span onclick="window.location.href='/task/index'" class="logo">GGECLABEL</span>
            </div>

            <div class="pageNav">
                <div class="pageControl">
                    <div class="pagePrev pageSwitch inline-block" title="上一张 (快捷键: D)"></div>
                    <div class="pageInfo inline-block">
                        <p class="pageName" title="名称">样本文件</p>
                        <p class="nameProcess" title="序号">
                            <input type="text" class="pageJumpInput" value="1" title="输入页码直接跳转" style="width: 50px; text-align: center; border: 1px solid #ccc; border-radius: 3px; padding: 2px;" placeholder="页码">
                            / <span class="processSum">0</span>
                        </p>
                    </div>
                    <div class="pageNext pageSwitch inline-block" title="下一张 (快捷键: F)"></div>
                </div>
            </div>


            <div class="assistTool">
                <div class="generalFeatures">

                    <p class="featureList toolRect xc_selected" title="绘制矩形 (快捷键: N - 自动使用默认标签)">
                        <i class="bg"></i>
                        <span>矩形</span>
                    </p>

                    <p class="featureList toolPolygon" title="绘制多边形">
                        <i class="bg"></i>
                        <span>多边形</span>
                    </p>

                    <p class="featureList toolDrag" title="拖拽">
                        <i class="bg"></i>
                        <span>拖拽</span>
                    </p>
                    <p class="featureList crossLine " title="十字线开关">
                        <input class="mui-switch mui-switch-anim" type="checkbox">
                        <span>十字线</span>
                    </p>
                    <p class="featureList labelShower" title="标注结果显示开关">
                        <input class="mui-switch mui-switch-anim" type="checkbox">
                        <span>标注结果</span>
                    </p>
                              <p class="featureList screenShot" title="快照">
                        <i class="bg"></i>
                        <span>快照</span>
                    </p>
                    <p class="featureList commonBtn toolTagsManager" title="标签管理">
                        <span>标签</span>
                    </p>
                    <p onclick="f_saveAnnotation()" class="featureList commonBtn" title="保存标注 (快捷键: Ctrl+S)">
                        <span>保存</span>
                    </p>
                    <p onclick="f_removeAnnotation()" class="featureList commonBtn" title="删除标注">
                        <span>删除</span>
                    </p>
                    <p onclick="f_deleteCurrentImage()" class="featureList commonBtn deleteImageBtn" title="删除当前图像">
                        <span>删除图像</span>
                    </p>
                    <p onclick="f_showKeyboardHelp()" class="featureList commonBtn" title="键盘快捷键帮助">
                        <span>快捷键</span>
                    </p>

                </div>
            </div>
        </div>
    </div>

    <div class="canvasMain">
        <div class="canvasContent">
            <canvas id="canvas"></canvas>
            <div class="scaleBox">
                <div class="scaleCanvas"></div>
                <div class="scalePanel"></div>
            </div>
        </div>
        <div class="commentResult">
            <!--
             <div class="historyContent">
                <p class="title">标签列表<a style="float: right;margin-right: 20px;">添加</a></p>
                <div class="historyGroup">
                    <p data-type="add" data-index="0" class="">人</p>
                    <p data-type="add" data-index="1" class="">狗</p>
                </div>
            </div>-->

            <div class="resultArea">
                <p class="title">标注结果 (<span class="resultLength">0</span>)</p>
                <div class="resultList_head">
                    <div class="headChildren">
                        <p class="headName">名称</p>
                        <p class="headEdit">修改</p>
                        <p class="headDelete">删除</p>
                        <p class="headDisplay">显/隐</p>
                    </div>
                </div>
                <div class="resultGroup">
                </div>
                <div class="resultSelectLabel">
                    <p class="selectLabelTip" hidden>请先创建标签</p>
                    <ul class="selectLabel-ul">
                    </ul>
                    <div class="closeLabelManage"><span class="icon-remove-sign"></span></div>
                </div>
            </div>

            <div class="tabBtn focus"><span class="icon-double-angle-right"></span></div>
        </div>
        <div class="labelManage">
            <div class="labelManage-Info">
                <div class="labelManage-menu">
                    <div class="labelManage-createLabel"><button class="button btn-primary labelManage-createButton">创建</button></div>
                </div>
                <div class="labelManage-subList">标签列表：</div>
                <div class="labelManage-group">
                    <p class="labelTip" hidden>请先创建标签</p>
                    <ul class="labelManage-ul">
                    </ul>
                </div>
            </div >
            <div class="labelManage-create" hidden>
                <div class="labelManage-Title">创建标签</div>
                <div class="labelCreate labelCreate-name">
                    <label>标签名称：</label>
                    <input type="text" class="labelCreate-nameInput">
                </div>
                <div class="labelCreate labelCreate-color">
                    <label>标签颜色：</label>
                    <span class="colorPicker" id="colorPicker"></span>
                    <input class="colorHex" id="colorHex" value="#ff0000" data-r="255" data-g="0" data-b="0" readonly>
                </div>
                <div class="labelCreate">
                    <button class="button btn-error removeLabel" title="删除标签">删除</button>
                </div>
                <div class="labelCreateButtons">
                    <button class="button btn-success addLabel">确定</button>
                    <button class="button btn-default closeAdd">取消</button>
                </div>
            </div>
            <div class="closeLabelManage"><span class="icon-remove-sign"></span></div>
        </div>
    </div>
</div>
</body>
<!-- jQuery -->
<script src="/static/lib/jquery/jquery.min.js"></script>
<script src="/static/lib/label/js/colorPicker.js"></script>
<script src="/static/lib/label/js/label.js?v=15"></script>

        <script src="/static/lib/jquery/toast/1.3.2/jquery.toast.min.js"></script>
        <script src="/static/lib/jquery/toast/1.3.2/toast.js"></script>
<script>
    let mTaskCode = "{{ task_code }}";
    let mSampleCode = "{{ sample_code }}";
    let storageDir_www = "{{ storageDir_www }}";
    let elePagePrevBtn = document.querySelector('.pagePrev');
    let elePageNextBtn = document.querySelector('.pageNext');
    let elePageName = document.querySelector('.pageName');         // 标注任务名称
    let elePageJumpInput = document.querySelector('.pageJumpInput'); // 页码跳转输入框
    let eleProcessSum = document.querySelector('.processSum');     // 当前标注任务总数
    let eleJsToolDrag = document.querySelector('.toolDrag');
    let eleJsToolRect= document.querySelector('.toolRect');
    let eleJsToolPolygon= document.querySelector('.toolPolygon');
    let eleJsToolTagsManager= document.querySelector('.toolTagsManager');

    let eleCanvas = document.getElementById('canvas');
    eleCanvas.width = eleCanvas.clientWidth;// 设置画布初始属性
    eleCanvas.height = eleCanvas.clientHeight;
    //canvas.style.background = "#8c919c";
    eleCanvas.style.background = "#ffffff";// 设置画布宽高背景色

    let mSampleData = [];
    let mSample = {};
    let mHandleSum = 0;
    let mHandleIndex = 0; //标定下标
    let mCurrentToolState = 'rectOn'; // 保存当前工具状态，默认为矩形
    let mLabelImage = new LabelImage({
        canvas: eleCanvas,
        scaleCanvas: document.querySelector('.scaleCanvas'),
        scalePanel: document.querySelector('.scalePanel'),
        annotateState: document.querySelector('.annotateState'),
        canvasMain: document.querySelector('.canvasMain'),
        resultGroup: document.querySelector('.resultGroup'),
        crossLine: document.querySelector('.crossLine'),
        labelShower: document.querySelector('.labelShower'),
        screenShot: document.querySelector('.screenShot'),
        colorHex: document.querySelector('#colorHex'),
        toolTagsManager: document.querySelector('.toolTagsManager')
    });

    function f_sample() {
        if(mHandleIndex >= mSampleData.length || mHandleIndex < 0){
            console.error("索引超出范围:", mHandleIndex, "总数:", mSampleData.length);
            return;
        }

        // 直接使用数组索引获取样本
        let targetSample = mSampleData[mHandleIndex];
        let __sample_code = targetSample["code"];
        console.log("导航到样本:", __sample_code, "数组索引:", mHandleIndex, "数据库ID:", targetSample.id);
        window.location.href = "/sample/index?task_code="+mTaskCode+"&sample_code="+__sample_code;
    }

    // 跳转到指定页码
    function f_jumpToPage(pageNumber) {
        // 验证输入
        if (isNaN(pageNumber) || pageNumber === null || pageNumber === undefined) {
            myAlert("请输入有效的页码数字", "warning", 2000);
            f_updatePageDisplay(); // 恢复正确显示
            return false;
        }

        // 验证页码范围 (1-based)
        if (pageNumber < 1 || pageNumber > mHandleSum) {
            myAlert("页码超出范围，请输入1到" + mHandleSum + "之间的数字", "warning", 2000);
            f_updatePageDisplay(); // 恢复正确显示
            return false;
        }

        // 如果跳转到当前页，不需要操作
        if (pageNumber === mHandleIndex + 1) {
            return true;
        }

        // 保存当前标注
        f_postSaveAnnotation(mHandleIndex, false);

        // 转换为0-based索引
        mHandleIndex = pageNumber - 1;

        // 跳转
        f_sample();
        return true;
    }

    // 更新页码显示
    function f_updatePageDisplay() {
        if (elePageJumpInput) {
            elePageJumpInput.value = mHandleIndex + 1; // 显示1-based页码
            elePageJumpInput.max = mHandleSum;
        }
    }


    function f_remove_tool_selected() {
        eleJsToolDrag.classList.remove('xc_selected');
        eleJsToolRect.classList.remove('xc_selected');
        eleJsToolPolygon.classList.remove('xc_selected');
        //for (let i=0; i<eleJsToolPolygon.children.length; i++) {
        //    eleJsToolPolygon.children[i].classList.remove('xc_selected');
        //}
    }
    eleJsToolDrag.onclick = function (){
        f_remove_tool_selected();
        eleJsToolDrag.classList.add('xc_selected');
        mLabelImage.SetFeatures('dragOn', true);
        mCurrentToolState = 'dragOn'; // 保存当前工具状态

    }
    eleJsToolRect.onclick = function (){
          console.log("矩形标注按钮被点击");
          f_remove_tool_selected();
          eleJsToolRect.classList.add('xc_selected');
          mLabelImage.SetFeatures('rectOn', true);
          mCurrentToolState = 'rectOn'; // 保存当前工具状态
          myAlert("矩形标注模式已激活，请在图像上拖拽绘制矩形", "info", 2000);
    }
    eleJsToolPolygon.onclick = function (){
          f_remove_tool_selected();
          eleJsToolPolygon.classList.add('xc_selected');
          mLabelImage.SetFeatures('polygonOn', true);
          mCurrentToolState = 'polygonOn'; // 保存当前工具状态
    }
    eleJsToolTagsManager.onclick = function(){
        f_remove_tool_selected();
        mLabelImage.SetFeatures('tagsOn', true);
    }

    elePagePrevBtn.onclick = function() {
        f_postSaveAnnotation(mHandleIndex, false);

        if (mHandleIndex <= 0) {
            mHandleIndex = mHandleSum - 1; // 循环到最后一张
        } else {
            mHandleIndex--;
        }

        f_sample();
    };

    elePageNextBtn.onclick = function() {
        console.log("点击下一张按钮，当前数组索引:", mHandleIndex, "总数:", mHandleSum);

        // 保存当前标注
        f_postSaveAnnotation(mHandleIndex, false);

        // 简单的数组索引递增逻辑
        if (mHandleIndex >= mHandleSum - 1) {
            mHandleIndex = 0; // 循环到第一张
        } else {
            mHandleIndex++; // 下一张
        }

        console.log("数组索引变化: ->", mHandleIndex);
        f_sample();
    };

    // 输入框跳转事件监听
    if (elePageJumpInput) {
        // 回车键跳转
        elePageJumpInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                let value = this.value.trim();
                if (value === '') {
                    myAlert("请输入页码", "warning", 1000);
                    return;
                }
                let pageNumber = parseInt(value);
                if (f_jumpToPage(pageNumber)) {
                    this.blur(); // 失去焦点
                }
            }
        });

        // 失去焦点时跳转
        elePageJumpInput.addEventListener('blur', function() {
            let value = this.value.trim();
            if (value === '') {
                // 如果为空，恢复当前页码显示
                f_updatePageDisplay();
                return;
            }
            let pageNumber = parseInt(value);
            if (!isNaN(pageNumber)) {
                f_jumpToPage(pageNumber);
            } else {
                // 输入无效，恢复当前页码显示
                f_updatePageDisplay();
            }
        });

        // 输入验证 - 允许空值以便用户可以完全删除
        elePageJumpInput.addEventListener('input', function() {
            let value = this.value.trim();
            if (value === '') {
                // 允许空值，用户可能正在输入
                return;
            }
            let numValue = parseInt(value);
            if (isNaN(numValue)) {
                // 如果不是数字，清空输入框
                this.value = '';
            } else if (numValue < 1) {
                this.value = '1';
            } else if (numValue > mHandleSum) {
                this.value = mHandleSum.toString();
            }
        });
    }
    function f_getInfo() {
        $.ajax({
               url: '/sample/getInfo',
               type: "get",
               async: true,
               data: {"task_code":mTaskCode,"sample_code":mSampleCode},
               dataType: "json",
               timeout: 0,
               error: function () {
                   myAlert("网络异常，请确定网络正常！","error");
               },
               success: function (res) {
                   if(res.code === 1000){

                       mSample = res["sample"];
                       let __sample_data = res["sample_data"];
                        try {
                            let __labels = mSample["labels"];
                            let __labels_obj = __labels ? JSON.parse(__labels) : [];
                            f_setLabels(__labels_obj);
                        }catch (e) {
                            console.log(e)
                        }

                       // 重置mSampleData数组
                       mSampleData = [];
                       let foundCurrentSample = false;

                       for (let i = 0; i < __sample_data.length; i++) {
                           let code = __sample_data[i]["code"];
                           let sampleItem = __sample_data[i];

                           mSampleData.push(sampleItem);

                           if(mSampleCode === code){
                               mHandleIndex = i; // 直接使用数组索引
                               foundCurrentSample = true;
                           }
                       }

                       // 如果没有找到当前样本，默认设置为第一个
                       if (!foundCurrentSample && mSampleData.length > 0) {
                           mHandleIndex = 0;
                           console.warn("当前样本代码未找到，默认设置为第一个样本");
                       }

                        mHandleSum = mSampleData.length
                        eleProcessSum.innerText = mHandleSum;
                        f_updatePageDisplay(); // 更新页码显示

                        console.log("样本数据加载完成:");
                        console.log("总样本数:", mHandleSum);
                        console.log("当前数组索引:", mHandleIndex);
                        console.log("当前样本代码:", mSampleCode);

                        // 验证当前索引对应的样本代码是否正确
                        if (mHandleIndex >= 0 && mHandleIndex < mSampleData.length) {
                            let currentSample = mSampleData[mHandleIndex];
                            console.log("数组索引", mHandleIndex, "对应的样本:", {
                                code: currentSample.code,
                                id: currentSample.id,
                                state: currentSample.state
                            });
                            if (currentSample.code !== mSampleCode) {
                                console.warn("警告：数组索引对应的样本代码不匹配！期望:", mSampleCode, "实际:", currentSample.code);
                            }
                        } else {
                            console.warn("警告：数组索引", mHandleIndex, "超出范围");
                        }

                        if(mHandleIndex < mHandleSum){
                            let sample_id = mSample["id"]
                            let sample_code = mSample["code"]
                            let task_code = mSample["task_code"]
                            let old_filename = mSample["old_filename"]
                            let new_filename = mSample["new_filename"]
                            let annotation_state = mSample["annotation_state"]
                            let annotation_content = mSample["annotation_content"]
                            let imageUrl = storageDir_www + "task/"+task_code+"/sample/"+new_filename;

                             elePageName.innerText = sample_id.toString();

                            try {
                                let __annotation_content_obj = JSON.parse(annotation_content);
                                console.log("__annotation_content_obj:",__annotation_content_obj)
                                mLabelImage.SetImage(imageUrl,__annotation_content_obj);
                            }catch (e) {
                                console.log(e)
                                mLabelImage.SetImage(imageUrl);
                            }

                            setTimeout(function() {
                                // 恢复之前保存的工具状态，而不是强制设置为矩形
                                mLabelImage.SetFeatures(mCurrentToolState, true);

                                // 更新UI按钮状态
                                f_remove_tool_selected();
                                if (mCurrentToolState === 'rectOn') {
                                    eleJsToolRect.classList.add('xc_selected');
                                } else if (mCurrentToolState === 'polygonOn') {
                                    eleJsToolPolygon.classList.add('xc_selected');
                                } else if (mCurrentToolState === 'dragOn') {
                                    eleJsToolDrag.classList.add('xc_selected');
                                }

                                mLabelImage.IsShowLabels();
                                mLabelImage.CrossHairSwitch();//设置开启十字线
                            }, 100);

                        }
                   }else{
                       //alert(res.msg);
                   }
               }
            });
    }
    function f_postSaveAnnotation(handleIndex,isForce) {

        if(handleIndex >= mSampleData.length){
            return;
        }
        if(isForce===false){
            //非强制执行状态下，标记内容为空时，无需执行保存
            if(mLabelImage.Arrays.imageAnnotateMemory.length === 0){
                return;
            }
        }

        let d = mSampleData[handleIndex];
        let sample_code = d["code"]
        let annotation_content = JSON.stringify(mLabelImage.Arrays.imageAnnotateMemory)
        let labels = JSON.stringify(mLabels);
        
        $.ajax({
           url: "/sample/postSaveAnnotation",
           type: "post",
           async: true,
           data: {
               "sample_code":sample_code,
               "annotation_content":annotation_content,
               "labels":labels
           },
           dataType: "json",
           timeout: 0,
           error: function () {
               myAlert("网络异常，请确定网络正常！","error");
           },
           success: function (res) {
               if(res.code === 1000){
                   myAlert(res.msg,"success",1000);
               }else{
                   myAlert(res.msg,"error",1000);
               }
           }
        });

    }
    function f_postDelAnnotation(handleIndex) {

        if(handleIndex >= mSampleData.length){
            return;
        }
        let d = mSampleData[handleIndex];
        let sample_code = d["code"]

        $.ajax({
           url: "/sample/postDelAnnotation",
           type: "post",
           async: true,
           data: {
               "sample_code":sample_code
           },
           dataType: "json",
           timeout: 0,
           error: function () {
                myAlert("网络异常，请确定网络正常！","error");
           },
           success: function (res) {
               if(res.code === 1000){
                   myAlert(res.msg,"success",1000);
               }else{
                   myAlert(res.msg,"error",1000);
               }
           }
        });

    }
    function f_saveAnnotation() {
        f_postSaveAnnotation(mHandleIndex,true);
    }
    function f_removeAnnotation() {
        // 添加确认提示框
        if (!confirm('确定要删除当前样本的标注吗？删除后无法恢复！')) {
            return;
        }
        f_postDelAnnotation(mHandleIndex);
    }

    // 删除当前图像
    function f_deleteCurrentImage() {
        // 添加确认提示框
        if (!confirm('确定要删除当前图像吗？删除后图像无法恢复！')) {
            return;
        }

        if(mHandleIndex >= mSampleData.length || mHandleIndex < 0){
            myAlert("无效的图像索引", "error", 2000);
            return;
        }

        let currentSample = mSampleData[mHandleIndex];
        let sample_code = currentSample["code"];

        $.ajax({
           url: "/sample/postDel",
           type: "post",
           async: true,
           data: {
               "code": sample_code
           },
           dataType: "json",
           timeout: 0,
           error: function () {
               myAlert("网络异常，请确定网络正常！","error");
           },
           success: function (res) {
               if(res.code === 1000){
                   myAlert(res.msg,"success",1000);

                   // 从数组中移除已删除的样本
                   mSampleData.splice(mHandleIndex, 1);
                   mHandleSum = mSampleData.length;

                   // 更新总数显示
                   eleProcessSum.textContent = mHandleSum;

                   // 如果删除的是最后一张图像，跳转到前一张
                   if (mHandleIndex >= mHandleSum && mHandleSum > 0) {
                       mHandleIndex = mHandleSum - 1;
                   }

                   // 如果还有图像，跳转到当前索引的图像
                   if (mHandleSum > 0) {
                       // 更新页码显示
                       elePageJumpInput.value = mHandleIndex + 1;
                       f_sample();
                   } else {
                       // 如果没有图像了，返回任务列表
                       myAlert("该任务已无图像，即将返回任务列表", "info", 2000);
                       setTimeout(function() {
                           window.location.href = '/task/index';
                       }, 2000);
                   }
               }else{
                   myAlert(res.msg,"error",1000);
               }
           }
        });
    }
    // 创建新标注（使用默认标签index=0）
    function f_createNewAnnotation() {
        // 使用LabelImage类的新方法创建带默认标签的标注
        if (mLabelImage.CreateNewAnnotationWithDefaultLabel()) {
            // 设置UI状态为矩形工具
            f_remove_tool_selected();
            eleJsToolRect.classList.add('xc_selected');
            mCurrentToolState = 'rectOn'; // 保存当前工具状态

            // 提示用户绘制矩形
            myAlert("请在图像上绘制矩形标注框（将自动使用默认标签）", "info", 2000);
        } else {
            myAlert("请先创建标签", "warning", 2000);
        }
    }

    // 删除当前选中的标注
    function f_deleteSelectedAnnotation() {
        // 获取当前选中的标注索引
        let selectedIndex = mLabelImage.Arrays.resultIndex;
        if (selectedIndex > 0) {
            // 添加确认提示框
            if (!confirm('确定要删除选中的标注吗？删除后无法恢复！')) {
                return;
            }
            // resultIndex是1-based，需要转换为0-based
            mLabelImage.DeleteSomeResultLabel(selectedIndex - 1);
            myAlert("已删除选中的标注", "success", 1000);
        } else {
            myAlert("请先选择要删除的标注", "warning", 1000);
        }
    }

    // 显示键盘快捷键帮助
    function f_showKeyboardHelp() {
        let helpText = `
键盘快捷键说明：

导航快捷键：
• D键 - 上一张图片
• F键 - 下一张图片

工具选择快捷键：
• R键 - 选择矩形工具
• M键 - 选择多边形工具
• V键 - 选择拖拽工具

标注操作快捷键：
• N键 - 创建新标注（使用默认标签）
• Ctrl+S - 保存标注
• Backspace - 删除选中的标注

右键选择标签功能：
• 标注框画完后，可以使用鼠标右键快捷选择标签。

页码跳转功能：
• 在页码输入框中输入数字后按回车或失去焦点即可跳转
• 支持1到总页数之间的任意跳转

注意：在输入框中时快捷键不生效
        `;
        alert(helpText.trim());
    }

    // 键盘快捷键处理
    function f_setupKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // 如果焦点在输入框中，不处理快捷键
            if ($(e.target).is('input, textarea') || $(e.target).hasClass('pageJumpInput')) {
                return;
            }

            switch(e.which) {
                case 68: // 'd' key - 上一个
                    e.preventDefault();
                    elePagePrevBtn.click();
                    break;

                case 70: // 'f' key - 下一个
                    e.preventDefault();
                    elePageNextBtn.click();
                    break;

                case 78: // 'n' key - 创建新标注
                    e.preventDefault();
                    f_createNewAnnotation();
                    break;

                case 77: // 'm' key - 多边形工具
                    e.preventDefault();
                    eleJsToolPolygon.click();
                    break;

                case 82: // 'r' key - 矩形工具
                    e.preventDefault();
                    eleJsToolRect.click();
                    break;

                case 86: // 'v' key - 拖拽工具
                    e.preventDefault();
                    eleJsToolDrag.click();
                    break;

                case 83: // 's' key
                    if (e.ctrlKey || e.metaKey) { // Ctrl+S 或 Cmd+S (Mac)
                        e.preventDefault();
                        f_saveAnnotation();
                    }
                    break;

                case 8: // Backspace key - 删除选中标注
                    e.preventDefault();
                    f_deleteSelectedAnnotation();
                    break;
            }
        });
    }

    window.onload = function() {
        console.log("页面加载完成，开始初始化");
        console.log("任务代码:", mTaskCode);
        console.log("样本代码:", mSampleCode);
        f_getInfo();
        f_setupKeyboardShortcuts();

        // 检查关键元素是否正确绑定
        console.log("矩形按钮元素:", eleJsToolRect);
        console.log("页码输入框元素:", elePageJumpInput);
    };
</script>
</html>