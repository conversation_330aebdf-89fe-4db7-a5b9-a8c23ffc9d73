{% extends "app/base_site.html" %}

{% block title %} 训练管理 {% endblock title %}

{% block stylesheets %}
  {{ block.super }}
    <style>
    .tabledata tbody tr:nth-child(even) {
      background-color: #073767;
    }

    .tabledata tbody tr:nth-child(odd) {
      background-color: #062b57;
    }
     .tabledata thead th {
        background-color: #0B4876;
    }
    .search-container {
      float: right;
      margin-right: 20px;
    }
    .search-input {
      padding: 5px;
      border-radius: 4px;
      border: 1px solid #ccc;
      margin-right: 5px;
    }
    .search-btn {
      padding: 5px 10px;
      border-radius: 4px;
      background-color: #0B4876;
      color: white;
      border: none;
      cursor: pointer;
    }
    .search-btn:hover {
      background-color: #073767;
    }
    </style>
{% endblock stylesheets %}

{% block content %}

      <div class="right_col" role="main">
    <div class="">
      <div class="row">
          <div class="col-md-12 col-sm-12 col-xs-12">
            <div class="x_panel">
              <div class="x_title">
               <h2>训练结果
                    <span id="top_loading" ><img class="top_loading_img" src="/static/images/load.gif" alt="loading">加载中</span>
                    <span id="top_msg">{{top_msg}}</span>
               </h2>
               <div class="search-container">
                 <input type="text" id="searchInput" class="search-input" placeholder="输入关键字搜索...">
                 <button onclick="filterTable()" class="search-btn">搜索</button>
               </div>
                  <div class="clearfix"></div>
              </div>
              <div class="x_content">



              <table class="table   table-condensed">
                                        <thead>
                                          <tr>
                                            <th>文件名</th>
                                            <th>操作</th>
                                          </tr>
                                        </thead>
                                        <tbody id="testModelData">
                                        </tbody>
                                    </table>


              </div>
            </div>
          </div>
      </div>

      <div class="row">
          <div class="col-md-12 col-sm-12 col-xs-12">
            <ul class="pagination">
                <li>
                    <span style="margin-right:10px;color:#000;">共<span id="pagesize">{{ pagesize }}</span>页 / <span>{{pageData.count}}</span >条</span>
                </li>

                {%  for d  in pageData.pageLabels%}
                  {% if d.cur == 1 %}
                    <li class="paginate_button active"><a href="#"  >{{ d.name }}</a></li>
                  {% else %}
                    <li class="paginate_button "><a href="/train/index?p={{d.page}}&ps={{pageData.page_size}}" >{{ d.name }}</a></li>
                  {% endif %}
                {% endfor %}

            </ul>

          </div>
       </div>


    </div>
  </div>
     <span id="test_model_loading" style="display: none;"><img class="top_loading_img" src="/static/images/load.gif" alt="loading">测试中</span>
{% endblock content %}

{% block javascripts %}
  {{ block.super }}

<script>
    let files = "{{ filelist }}";
    let eleTestModelLoading = $("#test_model_loading");
    let mTrainCode = "{{ train_code }}";
    let eleTestModelData = $("#testModelData");
    let storageDir_www = "{{ storageDir_www }}";
    let pagesize=0;
    let allTableData = []; // Store all table data for filtering

    function filterTable() {
        const searchText = document.getElementById('searchInput').value.toLowerCase();
        const rows = document.querySelectorAll('#testModelData tr');
        
        rows.forEach(row => {
            const fileName = row.querySelector('td:first-child').textContent.toLowerCase();
            if (fileName.includes(searchText)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Add event listener for Enter key
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            filterTable();
        }
    });

function f_trainTest_getIndex(t) {
    if(t!==0)
    {
        return
    }
    console.log("f_trainTest_getIndex")
    //eleTestModelLoading.show()

       let data = files.replace(/&#x27;/g, '') .replace(/', '/g, ',') .replace(/^\[|\]$/g, '').split(",");

                   let data_length = data.length;

                   let item_html;
                    console.log("000",data_length)
                   if(data_length === 0){
                        item_html = "";
                        item_html += "<tr class=\"even pointer\">";
                        item_html += "<td colspan='8'>暂无数据</td>";
                        item_html += "</tr>";
                        eleTestModelData.append(item_html);
                   }else{

                       pagesize=0;
                       for (let i = 0; i < data.length; i++) {


                           {#let file_type = 1;#}
                           {#File: E:\project\bxc\gitee\xclabel\static\storage/train/train20250428190557\train\args.yaml#}
                            let file_path = data[i];
                            //alert(file_path)
                           let file_name = file_path.split("\\")[file_path.split("\\").length-1];
                            let file_type = file_name.split(".")[1]
                            if(file_name.indexOf(".")<0)
                            {
                                continue
                            }
                            pagesize++;
                           item_html = "";
                            item_html += "<tr >";
                           {#item_html +="<tr><td>"+data[i]["id"]+"</td>";#}
                           {#item_html +="<td>"+data[i]["username"]+"</td>";#}
                           item_html +="<td>"+file_name+"</td>";
                           if(file_type === "png"||file_type === "jpg"){
                               let sourceUrl = storageDir_www + "train/"+mTrainCode+"/train/"+file_name;
                               let resultUrl = storageDir_www + "train/"+mTrainCode+"/train/"+file_name;

                               item_html +="<td><img onclick='f_openImg(\""+sourceUrl+"\")' src=\""+sourceUrl+"\" class=\"avatar\" style='cursor: pointer' alt=\"Avatar\"></td>";
                           }
                           else if(file_type === "pt"){
                               {#let predict_file_name = file_name.slice(0,file_name.length - 3) + "avi";#}
                               {#let sourceUrl = storageDir_www + "train/"+data[i]["train_code"]+"/test/"+data[i]["code"]+"/"+file_name;#}
                               {#let resultUrl = storageDir_www + "train/"+data[i]["train_code"]+"/test/"+data[i]["code"]+"/predict/"+predict_file_name;#}
                               {# item_html +="<td><a target='_blank' href='"+sourceUrl+"' >在线播放</a></td>";#}
                               {# item_html +="<td><a target='_blank' href='"+resultUrl+"'  >下载</a></td>";#}
                                item_html +="<td> <button onclick='f_downloadmodel(\"pt\",\""+file_name+"\")' class='btn btn-sm btn-default' type='button' data-placement='top' data-toggle='tooltip' data-original-title='下载'><i class='glyphicon glyphicon-download-alt'></i></button></td>";
                           }
                           else if(file_type === "bin"){
                                 item_html +="<td> <button onclick='f_downloadmodel(\"bin\",\""+file_name+"\")' class='btn btn-sm btn-default' type='button' data-placement='top' data-toggle='tooltip' data-original-title='下载'><i class='glyphicon glyphicon-download-alt'></i></button></td>";
                           }
                           else if(file_type === "xml"){
                                 item_html +="<td> <button onclick='f_downloadmodel(\"xml\",\""+file_name+"\")' class='btn btn-sm btn-default' type='button' data-placement='top' data-toggle='tooltip' data-original-title='下载'><i class='glyphicon glyphicon-download-alt'></i></button></td>";
                           }
                           else if(file_type === "csv"){
                                 item_html +="<td> <button onclick='f_downloadmodel(\"csv\",\""+file_name+"\")' class='btn btn-sm btn-default' type='button' data-placement='top' data-toggle='tooltip' data-original-title='下载'><i class='glyphicon glyphicon-download-alt'></i></button></td>";
                           }
                           else if(file_type === "yaml"){
                                 item_html +="<td> <button onclick='f_downloadmodel(\"yaml\",\""+file_name+"\")' class='btn btn-sm btn-default' type='button' data-placement='top' data-toggle='tooltip' data-original-title='下载'><i class='glyphicon glyphicon-download-alt'></i></button></td>";
                           }
                           else{
                                item_html +="<td>下载</td>";
                           }
                           {# item_html +="<td>"+data[i]["calcu_seconds"]+"(s)</td>";#}
                           {# item_html +="<td>"+data[i]["create_time_str"]+"</td>";#}
                           {#item_html +="<td><a href=\"javascript:f_trainTest_postDel('"+data[i]["code"]+"')\" class=\"btn btn-danger btn-xs\"><i class=\"fa fa-trash-o\"></i></a></td>";#}
                           item_html +="</tr>";
                            eleTestModelData.append(item_html);
                       }
                       $("#pagesize").html(pagesize)


                   }
}
function f_downloadmodel(type,name){
    // 构建正确的下载URL，不使用硬编码域名
    var baseUrl = window.location.protocol + "//" + window.location.host;
    var modelUrl = baseUrl + "/storage/download?filename=train/"+mTrainCode+"/train/"+name;

    if(type=="pt")
    {
         modelUrl = baseUrl + "/storage/download?filename=train/"+mTrainCode+"/train/weights/"+name;
    }
    if(type=="bin")
    {
         modelUrl = baseUrl + "/storage/download?filename=train/"+mTrainCode+"/train/weights/last_openvino_model/"+name;
    }
    if(type=="xml")
    {
         modelUrl = baseUrl + "/storage/download?filename=train/"+mTrainCode+"/train/weights/last_openvino_model/"+name;
    }

    // 先检查文件是否存在（对于重要文件类型）
    if(type=="pt" || type=="bin" || type=="xml") {
        var checkFilename = "train/"+mTrainCode+"/train/";
        if(type=="pt") {
            checkFilename += "weights/"+name;
        } else {
            checkFilename += "weights/last_openvino_model/"+name;
        }

        $.ajax({
            url: '/storage/access',
            type: "get",
            async: true,
            data: {"filename": checkFilename},
            timeout: 5000,
            success: function(data, textStatus, xhr) {
                // 文件存在，可以下载
                window.open(modelUrl);
            },
            error: function(xhr, textStatus, errorThrown) {
                // 文件不存在或其他错误
                if (xhr.status === 404) {
                    myAlert("文件不存在：" + name, "error");
                } else if (xhr.responseJSON && xhr.responseJSON.msg) {
                    if (xhr.responseJSON.msg.includes("not found")) {
                        myAlert("文件不存在：" + name, "error");
                    } else {
                        myAlert("检查文件时发生错误：" + xhr.responseJSON.msg, "error");
                    }
                } else {
                    myAlert("检查文件时发生错误，请稍后重试", "error");
                }
            }
        });
    } else {
        // 对于其他文件类型，直接下载
        window.open(modelUrl);
    }
}
function f_openImg(imgUrl) {
    window.open(imgUrl)
}
 $(document).ready(function () {
    f_trainTest_getIndex(0);
});
</script>

{% endblock javascripts %}

